defines = -DENABLE_LOG_OBFUSCATION -DUSE_OPENSSL_CRYPTO
include_dirs = -I../../middleware/mlog/utils/LogObfuscator -I../../platform/D2J/build/appsdk/minieye/include -I../../platform/D2J/build/appsdk/horzion/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG -fPIE
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG -fPIE
target_output_name = mlogdeobf

build obj/middleware/mlog/utils/mlogdeobf.mlogdeobf.o: cxx ../../middleware/mlog/utils/mlogdeobf.cpp
build obj/middleware/mlog/utils/LogObfuscator/mlogdeobf.LogObfuscator.o: cxx ../../middleware/mlog/utils/LogObfuscator/LogObfuscator.cpp
build obj/middleware/mlog/utils/LogObfuscator/mlogdeobf.MiniAESGCM.o: cxx ../../middleware/mlog/utils/LogObfuscator/MiniAESGCM.cpp

build bin/mlogdeobf: link obj/middleware/mlog/utils/mlogdeobf.mlogdeobf.o obj/middleware/mlog/utils/LogObfuscator/mlogdeobf.LogObfuscator.o obj/middleware/mlog/utils/LogObfuscator/mlogdeobf.MiniAESGCM.o
  ldflags = -l$:libssl.a -l$:libcrypto.a -ldl -L/home/<USER>/project/d2j/out/D2J_release -Wl,-rpath-link=/home/<USER>/project/d2j/out/D2J_release -lpthread -L/home/<USER>/project/d2j/platform/D2J/build/appsdk/minieye/lib -Wl,-rpath-link=/home/<USER>/project/d2j/platform/D2J/build/appsdk/minieye/lib -L/home/<USER>/project/d2j/platform/D2J/build/appsdk/horzion/lib -Wl,-rpath-link=/home/<USER>/project/d2j/platform/D2J/build/appsdk/horzion/lib
  libs =
  output_extension = 
  output_dir = bin
