defines = -DENABLE_LOG_OBFUSCATION -DUSE_OPENSSL_CRYPTO
include_dirs = -I../../middleware/logd/src/include -I../../middleware/logd/src/base/include -I../../middleware/logd/src/pcre/dist -I../../middleware/logd/src/pcre -I../../middleware/logd/src/logcat/LogObfuscator -I../../platform/D2J/build/appsdk/minieye/include -I../../platform/D2J/build/appsdk/horzion/include -I../../middleware/logd/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG -fPIE
cflags_cc = -fpermissive -std=c++17 -Wno-implicit-fallthrough -Wno-unused-function -Wno-unused-variable -Wno-comment -Wno-unused-result -Wno-format -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG -fPIE
target_output_name = logdcat

build obj/middleware/logd/src/logcat/logdcat.logcat.o: cxx ../../middleware/logd/src/logcat/logcat.cpp
build obj/middleware/logd/src/logcat/LogObfuscator/logdcat.LogObfuscator.o: cxx ../../middleware/logd/src/logcat/LogObfuscator/LogObfuscator.cpp
build obj/middleware/logd/src/logcat/LogObfuscator/logdcat.MiniAESGCM.o: cxx ../../middleware/logd/src/logcat/LogObfuscator/MiniAESGCM.cpp

build bin/logdcat: link obj/middleware/logd/src/logcat/logdcat.logcat.o obj/middleware/logd/src/logcat/LogObfuscator/logdcat.LogObfuscator.o obj/middleware/logd/src/logcat/LogObfuscator/logdcat.MiniAESGCM.o ./liblogd.so ./libcutils_logd.a ./libbase_logd.a ./libpcrecpp_logd.a ./libpcre_logd.a
  ldflags = -l$:libssl.a -l$:libcrypto.a -ldl -L/home/<USER>/project/d2j/out/D2J_release -Wl,-rpath-link=/home/<USER>/project/d2j/out/D2J_release -lpthread -L/home/<USER>/project/d2j/platform/D2J/build/appsdk/minieye/lib -Wl,-rpath-link=/home/<USER>/project/d2j/platform/D2J/build/appsdk/minieye/lib -L/home/<USER>/project/d2j/platform/D2J/build/appsdk/horzion/lib -Wl,-rpath-link=/home/<USER>/project/d2j/platform/D2J/build/appsdk/horzion/lib
  libs =
  output_extension = 
  output_dir = bin
