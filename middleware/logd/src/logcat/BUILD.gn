import("//build/minieye/minieye.gni")

MLOG_WITH_OBFUCATED = "false"

if (platform_name == "D2J") {
    MLOG_WITH_OBFUCATED = "true"
}

config("obfuscation_config") {
    if (!defined(include_dirs)) {
        include_dirs = []
    }
    include_dirs += [
        "LogObfuscator",
    ]

    if (!defined(ldflags)) {
        ldflags = []
    }
    ldflags += [
        "-l:libssl.a",
        "-l:libcrypto.a",
        "-ldl",
    ]

    if (!defined(defines)) {
        defines = []
    }
    defines += [
        "ENABLE_LOG_OBFUSCATION",
        "USE_OPENSSL_CRYPTO",
    ]
}

minieye_executable("logdcat") {
    sources = [
        "logcat.cpp",
    ]

    include_dirs = [
        "../include",
        "../base/include",
        # "//middleware/daemon/src/property",
        "../pcre/dist/",
        "../pcre"
    ]

    cflags_cc = [
        "-fpermissive",
        "-std=c++17",
        "-Wno-implicit-fallthrough",
        "-Wno-unused-function",
        "-Wno-unused-variable",
        "-Wno-comment",
        "-Wno-unused-result",
        "-Wno-format"
    ]
    cflags_c = [
        "-std=c11",
        "-Wno-unused-result",
    ]
    deps = [
        "../liblog:liblogd",
        "../libcutils:libcutils_logd",
        "../base:base_logd",
        "../pcre:libpcrecpp_logd"
    ]

    if (MLOG_WITH_OBFUCATED == "true") {
        configs = [ ":obfuscation_config" ]

        sources += [
            "LogObfuscator/LogObfuscator.cpp",
            "LogObfuscator/MiniAESGCM.cpp",
        ]

        deps += [
            ":mlogdeobf",
        ]
    }
}

if (MLOG_WITH_OBFUCATED == "true") {
    minieye_executable("mlogdeobf") {
        sources = [
            "mlogdeobf.cpp",
            "LogObfuscator/LogObfuscator.cpp",
            "LogObfuscator/MiniAESGCM.cpp",
        ]

        configs = [ ":obfuscation_config" ]
    }
}
