/*
 * Copyright [2025] MINIEYE
 * Descripttion : 日志混淆器，使用UUID+AES-128-GCM进行日志加密
 * Author       : xu<PERSON><PERSON>n
 * Date         : 2025-08-06
 */

#pragma once

#include <string>
#include <vector>
#include <cstdint>

namespace minieye {
namespace mlog {

class LogObfuscator {
 public:
    // 混淆
    static std::string obfuscate(const std::string& logLine);

    // 反混淆
    static std::string deobfuscate(const std::string& obfuscatedLine);

    // 初始化
    static bool initialize();

    // 获取UUID接口（预留，空实现）
    static std::string getUUID();

    // 设置UUID
    static bool setUUID(const std::string& uuid);

 private:
    static std::vector<uint8_t> s_aesKey;  // 当前AES密钥
    static std::string s_uuid;             // 当前UUID
    static bool s_initialized;             // 是否已初始化

    // 从UUID生成AES密钥
    static std::vector<uint8_t> generateAESKeyFromUUID(const std::string& uuid);

    // UUID格式验证
    static bool isValidUUID(const std::string& uuid);

    static std::string base64Encode(const std::vector<uint8_t>& data);
    static std::vector<uint8_t> base64Decode(const std::string& encoded);
};

}  // namespace mlog
}  // namespace minieye
