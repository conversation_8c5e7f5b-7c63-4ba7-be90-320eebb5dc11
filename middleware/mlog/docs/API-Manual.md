# mlog api

    MINIEYE log（简称 mlog）是MINIEYE提供的日志管理库

## 初期设置

---

    mlog模块支持终端log输出，本地log文件输出，syslog输出，dlt输出。

    mlog的初期设定通过宏函数`MLOG_INIT(LogConfig config)`来实施，每个进程中 `MLOG_INIT`函数只能调用一次，其后的调用没有效果，并且需要在mlog开始输出之前完成对config的赋值和此函数的调用。示例代码如下：

```c++
#include "mlog/Logging.h"

minieye::mlog::LogConfig config;
config.level = minieye::mlog::LogTrace;  // log level
config.tag = "MINIEYE";  // log tag name
config.isLogFunc = true;  // print Function file name and number of lines
config.isLogFile = false;  // log file save or not
config.savePath = "destination";  // log file save path
config.logFileName = "minieye.log";  // log file save name
config.fileSize = 2 * 1024 * 1024;  // save file size
config.fileCount = 10;  // save file count
config.isSyslog = false;  // print to syslog or not
config.isRemoteLog = false;  // print to mlogcat or not
config.isAsync = true;  // use async logger or sync logger
config.isAsyncBlock = true;  // define the handle policy when the queue is full
config.asyncMaxItem = 8192;   // define the max item of log queue
config.logDomain = minieye::mlog::DomainAlgo;  // remote log buffer

MLOG_INIT(config);
```

## 设置说明

---

**minieye::mlog::LogConfig::level**可以设置以下的数值(等级由低到高)：

| LOG等级                    |
| -------------------------- |
| minieye::mlog::LogTrace    |
| minieye::mlog:: LogDebug   |
| minieye::mlog::LogInfo     |
| minieye::mlog::LogWarn     |
| minieye::mlog::LogError    |
| minieye::mlog::LogCritical |
| minieye::mlog::LogOff      |
|                            |

等级低于此level的log不会被输出。

**tag**为用户想要输出的log标识符，用来区分不同进程的log。此tag也被用于mlogcat命令区别模块。

**isLogFunc**为true时，打印日志中会带有函数所在文件和行数。（默认true）

**isLogFile**为true时，log输出的同时会保存到文件中。（默认false）

**savePath**为保存文件的路径。

- 路径建议为 `/data/mlog/xxx`，方便统一管理。
- 尽量不要在量产设备出现记录Log文件的情况，避免设备频繁刷写flash，影响设备寿命。

**logFileName**为保存文件的名称。

**fileSize**为保存的单个文件大小（单位：Byte/ 范围: > 0），默认值为 2x*1024x*1024 Bytes = 2 MB。

**fileCount**为保存的最大文件数，默认值为 10（范围: > 0）。

**isSyslog**为true时候，日志会同步输出到syslogd。（默认为false）

**isRemoteLog**为true时，日志会同步输出到mlogcat。（默认为false）

- 一般情况下都建议使用isRemoteLog=true，这样在程序运行时候，可以通过mlogcat工具查看log信息和控制log等级。
- 当isRemoteLog=true，请注意设置默认等级不要太高，并且有效控制默认log的输出频率，以免频繁刷新log影响其他模块

**isAsync**为是否使用异步logger。（默认为true）

- 设置为true时，使用异步logger。log的落盘等操作都是在mlog自身线程中完成，不影响工作线程。推荐使用异步logger来进行log的输出。
- 设置为false时，使用同步logger。同步logger的log落盘均在工作线程中完成，可能会影响工作线程的处理。 只有isAsync设置为true时，isAsyncBlock和asyncMaxItem才会生效。

**isAsyncBlock**为指定log队列写满之后，继续输出log的策略。（默认为true）

- 设置为true时，等待已经在队列中的log写完一条后再加入队列末尾，会阻塞当前工作线程。
- 设置为false时，直接删除队列中最旧条目并将当前log加入队列末尾，不会阻塞当前工作线程。此选项有丢失log的风险

**asyncMaxItem**为缓存log的最大条目数。默认值为 8192，有效范围为：1-40*1024。如果用户设定的缓存最大条目数不在有效范围，将一律按照默认值8192来进行处理

**logDomain**为远程打印使用的buffer空间，有以下几种选择，不同使用者请挑选正确的domain

```c
enum LogDomain {
    DomainUser = 0, // 第三方供应商
    DomainApp, // MINIEYE 底软应用
    DomainBsp, // MINIEYE 底软BSP
    DomainAlgo, // MINIEYE 算法模块
    DomainMax,
};
```

## Log输出接口和说明

---

    mlog包含以下log输出的公开接口，且支持多线程调用：

```c++
#include "mlog/Logging.h"

// 输出LOG封装宏
MLOG_T(...)
MLOG_D(...)
MLOG_I(...)
MLOG_W(...)
MLOG_E(...)
MLOG_C(...)

// 满足条件输出
MLOG_T_IF(cond, ...)
MLOG_D_IF(cond, ...) 
MLOG_I_IF(cond, ...)
MLOG_W_IF(cond, ...) 
MLOG_E_IF(cond, ...) 
MLOG_C_IF(cond, ...) 

// 间隔输出，只打印前面几句
MLOG_FIRST_COUNT(level, n, ...)

// 间隔输出，每隔几次打印一句
MLOG_EVERY_COUNT(level, n, ...)

// 间隔输出，间隔多长时间打印一次
MLOG_EVERY_TIME(level, ms, ...)  
```

## Log输出用例

---

    log通过函数调用输出，支持fmt::format格式的字符参数设置，示例代码如下：

```c++
#include "mlog/Logging.h"

MLOG_T("OutPut Log TRACE");
MLOG_D("OutPut Log DEBUG");
MLOG_I("OutPut Log INFO");
MLOG_W("OutPut Log WARN");
MLOG_E("OutPut Log ERROR");
MLOG_C("OutPut Log Critical");

MLOG_E("OutPut Log case 1 {}", 1.2);
MLOG_E("OutPut Log case 2 {}", 3000);
MLOG_E("OutPut Log case 3 {1} {0} {2}", 1, 2, 3);
MLOG_E_IF(1 != 2, "OutPut Log case 4 {}", "MLOG_C_IF");
// 异常case测试，参数个数和format不一致
MLOG_E("test {} {} {}", "para1 {", 111);
```

    输出结果：

```C++
2023-07-11 14:53:01:961 20857 20857 T MINIEYE : [sample_mlog.cpp:114] OutPut Log TRACE
2023-07-11 14:53:01:961 20857 20857 D MINIEYE : [sample_mlog.cpp:115] OutPut Log DEBUG
2023-07-11 14:53:01:961 20857 20857 I MINIEYE : [sample_mlog.cpp:116] OutPut Log INFO
2023-07-11 14:53:01:961 20857 20857 W MINIEYE : [sample_mlog.cpp:117] OutPut Log WARN
2023-07-11 14:53:01:961 20857 20857 E MINIEYE : [sample_mlog.cpp:118] OutPut Log ERROR
2023-07-11 14:53:01:961 20857 20857 C MINIEYE : [sample_mlog.cpp:119] OutPut Log Critical
2023-07-11 14:53:01:961 20857 20857 E MINIEYE : [sample_mlog.cpp:121] OutPut Log case 1 1.2
2023-07-11 14:53:01:961 20857 20857 E MINIEYE : [sample_mlog.cpp:122] OutPut Log case 2 3000
2023-07-11 14:53:01:961 20857 20857 E MINIEYE : [sample_mlog.cpp:123] OutPut Log case 3 2 1 3
2023-07-11 14:53:01:961 20857 20857 E MINIEYE : [sample_mlog.cpp:124] OutPut Log case 4 MLOG_C_IF
// 异常case测试结果
// 输出异常产生的原因（捕获的异常）
[EXCEPTION] reason: argument not found
// 输出产生异常的位置
[EXCEPTION] position: ../../middleware/mlog/sample/sample_mlog.cpp : 126
```

    mLog输出的log中包含以下内容（以上面第一句打印为例子）：

| 样例                    | 格式解释                         |
| ----------------------- | -------------------------------- |
| 2023-07-11 14:53:01:961 | 年，月，日，小时，分钟，秒，毫秒 |
| 20857 20857             | 进程ID，线程ID                   |
| T                       | 打印等级                         |
| MINIEYE                 | tag                              |
| [sample_mlog.cpp:114]   | 文件名和行数，可以配置取消       |
| OutPut Log TRACE        | 文本输出                         |
|                         |                                  |
|                         |                                  |

## Log辅助函数接口

---

数据以16进制打印输出。

```C++
#include "mlog/Logging.h"
#include "mlog/HexDump.h"

// hexdump case 1
std::vector<char> buf(80);
for (int32_t i = 0; i < 80; i++) {
    buf.push_back(static_cast<char>(i & 0xff));
}
MLOG_E("hexdump 1: {:a}", minieye::mlog::toHex(buf));

// hexdump case 2
MLOG_E("hexdump 2: {:n}", minieye::mlog::toHex(std::begin(buf), std::begin(buf) + 10));

// hexdump case 3
char buffer[100];
for (int32_t i = 0; i < 100; i++) {
    buffer[i] = static_cast<char>(i & 0xff);
}
std::vector<char> vec = std::vector<char>(buffer, buffer + 100);
MLOG_E("hexdump 3: {:a}", minieye::mlog::toHex(vec));
```

打印函数执行时间

```c++
#include "mlog/Logging.h"
#include "mlog/StopWatch.h" 

int32_t count = 5;
while (count--) {
    minieye::mlog::StopWatch sw;
    usleep(10 * 1000);
    MLOG_E("test StopWatch {}", sw);
}
```

## Log输出指定格式

---

```C
    // mlog 指定输出格式
    // 1、字符对齐，10代表width参数
    // "<" = 左对齐
    // ">" = 右对齐
    // "^" = 居中
    MLOG_E("|{:^10}|{:<10}|{:>10}|", "first", "second", "third");
    // 2、2进制输出，b或者B表示输出是二进制前缀0b或0B
    MLOG_E("{:#b} {:#B}", 18, 18);
    // 3、16进制输出，x或者X表示输出是二进制前缀0x或0X
    MLOG_E("{:#x} {:#X}", 18, 18);
    // 4、字符输出
    MLOG_E("{:#c}", 33);
    // 5、浮点型输出，.3代表保留3位小数点
    const float pi = 3.1415926;
    MLOG_E("{:#f} {:.3f}", pi, pi);
```

## 日志混淆工具

---

mlog提供了日志混淆功能，通过mlogcat和mlogdeobf工具实现日志的加密和解密。

### mlogcat混淆选项

**-o选项**: 输出混淆后的日志
**-u选项**: 指定用于加密的UID（与-o配合使用）

```bash
mlogcat -o -u <uid> [其他选项]
```

### mlogdeobf解密工具

**功能**: 解密由`mlogcat -o -u`生成的混淆日志

**用法**:
```bash
# 从文件解密
mlogdeobf <encrypted_log_file>

# 从管道解密
cat encrypted.log | mlogdeobf

# 实时解密
mlogcat -o -u <uid> | mlogdeobf
```

### 使用示例

```bash
# 生成混淆日志文件
mlogcat -o -u "my-secret-uid-123" -f encrypted.log

# 解密查看日志
mlogdeobf encrypted.log

# 实时混淆和解密
mlogcat -o -u "my-secret-uid-123" | mlogdeobf | grep "ERROR"

# 混淆特定模块的日志
mlogcat -o -u "my-secret-uid-123" -s MINIEYE:I > module_encrypted.log
```
