/*
 * Copyright [2025] MINIEYE
 * Descripttion : mlog反混淆工具
 * Author       : xu<PERSON>esen
 * Date         : 2025-08-06
 */

#include "LogObfuscator.h"
#include <iostream>
#include <fstream>
#include <string>
#include <getopt.h>
#include <unistd.h>

/**
 * Print usage information of tool.
 */
static void usage() {
    fprintf(stderr, "mlogdeobf - Log deobfuscation utility\n\n");
    fprintf(stderr, "Usage:\n");
    fprintf(stderr, "  mlogdeobf [options] [file]\n");
    fprintf(stderr, "  mlogcat -o | mlogdeobf\n\n");
    fprintf(stderr, "Options:\n");
    fprintf(stderr, "  -h, --help     Show this help message\n");
    fprintf(stderr, "  -f <file>      Read from file instead of stdin\n\n");
    fprintf(stderr, "Examples:\n");
    fprintf(stderr, "  # Deobfuscate from file\n");
    fprintf(stderr, "  mlogdeobf -f obfuscated.log\n");
    fprintf(stderr, "  mlogdeobf obfuscated.log\n\n");
    fprintf(stderr, "  # Deobfuscate from pipe\n");
    fprintf(stderr, "  mlogcat -o | mlogdeobf\n");
    fprintf(stderr, "  cat obfuscated.log | mlogdeobf\n\n");
}

int main(int argc, char* argv[]) {
    std::string filename;
    bool use_stdin = true;

    int c;
    while ((c = getopt(argc, argv, "hf:")) != -1) {
        switch (c) {
            case 'h':
                usage();
                return 0;
            case 'f':
                filename = optarg;
                use_stdin = false;
                break;
            default:
                usage();
                return 1;
        }
    }

    if (use_stdin && optind < argc) {
        filename = argv[optind];
        use_stdin = false;
    }

    // 从stdin读
    std::istream* input_stream = &std::cin;
    std::ifstream file_stream;

    // 从文件读
    if (!use_stdin) {
        file_stream.open(filename);
        if (!file_stream.is_open()) {
            fprintf(stderr, "Error: Cannot open file '%s'\n", filename.c_str());
            return 1;
        }
        input_stream = &file_stream;
    }

    std::string line;

    while (std::getline(*input_stream, line)) {
        if (line.empty()) {
            std::cout << std::endl;
            continue;
        }

        // 检查是否是UID头部
        static const std::string keyPrefix = "MLOG_UID:";
        if (line.substr(0, keyPrefix.length()) == keyPrefix) {
            std::string uid = line.substr(keyPrefix.length());
            minieye::mlog::LogObfuscator::setUID(uid);
            // 不输出UID行，继续处理下一行
            continue;
        }

        // 检查是否是特殊的分隔符行（不需要解密，直接输出）
        if (line.find("--------- beginning of") != std::string::npos ||
            line.find("--------- switch to") != std::string::npos) {
            std::cout << line << std::endl;
            continue;
        }

        // 反混淆
        std::string deobfuscated = minieye::mlog::LogObfuscator::deobfuscate(line);
        std::cout << deobfuscated << std::endl;
    }

    if (!use_stdin) {
        file_stream.close();
    }

    return 0;
}
