# mlog日志混淆功能指南

## 概述

mlog日志混淆功能提供了日志加密解决方案，采用RSA3072+AES-128-GCM混合加密算法，确保敏感日志信息的安全性。该功能通过`mlogcat -o`和`mlogdeobf`工具实现日志的加密和解密。

## 技术架构

### 加密算法

- **RSA3072**: 非对称加密，用于保护AES密钥
- **AES-128-GCM**: 对称加密，用于加密日志内容，提供认证加密
- **多层密钥混淆**: RSA密钥经过4层混淆算法处理

### 密钥管理

```mermaid
graph TB
    A[原始RSA3072密钥对] --> B[多层混淆算法]
    B --> C[XOR混淆 0x5A]
    C --> D[字节循环左移3位]
    D --> E[相邻字节对交换]
    E --> F[位置加法混淆]
    F --> G[混淆后的密钥数据]
    G --> H[编译时内置到程序中]
```

## 工作流程

### 加密流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant M as mlogcat
    participant L as LogObfuscator
    participant R as RSA模块
    participant A as AES模块
    
    U->>M: mlogcat -o
    M->>L: 初始化混淆器
    L->>L: 生成随机AES-128密钥和IV
    L->>R: 使用RSA3072公钥加密AES密钥
    R-->>L: 返回加密的AES密钥
    L->>M: 输出密钥头部
    M->>U: MLOG_ENCRYPTED_KEY:base64密钥
    
    loop 每条日志
        M->>L: 原始日志内容
        L->>A: 使用AES-128-GCM加密
        A-->>L: 加密后的日志
        L->>M: base64编码的密文
        M->>U: 输出加密日志
    end
```

### 解密流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant D as mlogdeobf
    participant L as LogObfuscator
    participant R as RSA模块
    participant A as AES模块
    
    U->>D: 加密日志输入
    D->>L: 解析密钥头部
    L->>R: 使用RSA3072私钥解密AES密钥
    R-->>L: 返回AES密钥和IV
    
    loop 每条加密日志
        D->>L: base64密文
        L->>A: 使用AES-128-GCM解密
        A-->>L: 原始日志内容
        L->>D: 明文日志
        D->>U: 输出原始日志
    end
```

## 安全特性

### 密钥保护

1. **编译时混淆**: RSA密钥在编译时经过多层混淆算法处理
2. **静态分析防护**: 混淆算法增加逆向工程难度
3. **运行时解混淆**: 程序运行时动态恢复密钥

### 加密强度

- **RSA3072**: 提供等效于128位对称密钥的安全强度
- **AES-128-GCM**: 提供加密和完整性验证
- **动态密钥**: 每次运行生成新的AES密钥

### 完整性保护

```mermaid
graph LR
    A[原始日志] --> B[AES-128-GCM加密]
    B --> C[密文 + 认证标签]
    C --> D[Base64编码]
    D --> E[输出]
    
    F[加密日志] --> G[Base64解码]
    G --> H[AES-128-GCM解密]
    H --> I{认证标签验证}
    I -->|通过| J[原始日志]
    I -->|失败| K[解密失败]
```

## 使用指南

### 基本用法

```bash
# 生成加密日志
mlogcat -o > encrypted.log

# 解密日志
mlogdeobf encrypted.log

# 实时加密和解密
mlogcat -o | mlogdeobf
```

### 高级用法

```bash
# 加密特定模块日志
mlogcat -o -s MINIEYE:I > module_encrypted.log

# 加密并保存到文件
mlogcat -o -f /data/logs/encrypted.log

# 过滤解密后的日志
mlogcat -o | mlogdeobf | grep "ERROR"

# 解密并保存到文件
mlogdeobf encrypted.log > decrypted.log
```

## 文件格式

### 加密日志格式

```
MLOG_ENCRYPTED_KEY:NkPVY4/dI87y1JwRaWPYTyqACaEBCj7IZl7LofZ1bt0...
oYWc4E3IHi9mmMWUvMNDHbAOhBsJ7+rwv2MLES9O8XtFYrYO32TkNqvNBT8WLkoX...
oYWc4E3IHi9mmMWUnEjj+flPjqMyQ71I8drFTTJS8WZZb6kRyHa+af6dRWlKcQ0X...
```

- **第一行**: `MLOG_ENCRYPTED_KEY:` + Base64编码的加密AES密钥
- **后续行**: Base64编码的加密日志内容

### 密钥头部结构

```
MLOG_ENCRYPTED_KEY:[RSA加密的AES密钥(Base64)]
```

其中RSA加密的内容包含：
- AES-128密钥 (16字节)
- AES-128 IV (12字节)

## 性能考虑

### 加密性能

- **RSA加密**: 仅在启动时执行一次，性能影响极小
- **AES加密**: 高效的对称加密，适合大量数据处理
- **内存占用**: 增加约50KB静态内存占用

## 故障排除

### 常见问题

1. **解密失败**: 检查密钥头部是否完整
2. **性能问题**: 考虑使用管道而非文件方式
3. **编译错误**: 确保OpenSSL库正确链接
