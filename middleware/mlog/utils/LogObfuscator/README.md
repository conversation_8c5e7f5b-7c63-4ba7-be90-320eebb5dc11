# mlog日志混淆功能指南

## 概述

mlog日志混淆功能提供了日志加密解决方案，采用UID派生AES-128-GCM加密算法，确保敏感日志信息的安全性。该功能通过`mlogcat -o -u <uid>`和`mlogdeobf`工具实现日志的加密和解密。

## 技术架构

### 加密算法

- **UID派生**: 使用用户提供的UID通过复杂变换生成AES密钥
- **AES-128-GCM**: 对称加密，用于加密日志内容，提供认证加密
- **确定性密钥生成**: 相同UID永远生成相同的AES密钥

### 密钥管理

```mermaid
graph TB
    A[用户提供UID] --> B[SHA256哈希]
    A --> C[反向UID]
    C --> D[SHA256哈希]
    B --> E[h1: SHA256结果]
    D --> F[h2: SHA256结果]
    E --> G[h1 + h2]
    F --> G
    G --> H[SHA256哈希]
    H --> I[h3: SHA256结果]
    E --> J[位运算混合]
    F --> J
    I --> J
    J --> K[16字节AES-128密钥]
```

### UID密钥派生算法详解

1. **第一步**: 对原始UID进行SHA256哈希 → h1
2. **第二步**: 将UID反向后进行SHA256哈希 → h2
3. **第三步**: 将h1和h2拼接后进行SHA256哈希 → h3
4. **第四步**: 位运算混合生成16字节密钥
   - 从三个摘要按不同偏移取字节：`a=h1[i], b=h2[(i+8)%32], c=h3[(i*3)%32]`
   - 对b进行循环左移1位：`rot = (b<<1)|(b>>7)`
   - 异或混合：`key[i] = a ^ rot ^ (c + i)`

## 工作流程

### 加密流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant M as mlogcat
    participant L as LogObfuscator
    participant A as AES模块

    U->>M: mlogcat -o -u <uid>
    M->>L: 设置UID
    M->>L: 初始化混淆器
    L->>L: 从UID派生AES-128密钥
    L->>M: 输出UID头部
    M->>U: MLOG_UID:<uid>

    loop 每条日志
        M->>L: 原始日志内容
        L->>A: 使用AES-128-GCM加密(随机IV)
        A-->>L: 加密后的日志
        L->>M: base64编码的密文
        M->>U: 输出加密日志
    end
```

### 解密流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant D as mlogdeobf
    participant L as LogObfuscator
    participant A as AES模块

    U->>D: 加密日志输入
    D->>L: 解析UID头部
    L->>L: 从UID派生AES-128密钥

    loop 每条加密日志
        D->>L: base64密文
        L->>A: 使用AES-128-GCM解密
        A-->>L: 原始日志内容
        L->>D: 明文日志
        D->>U: 输出原始日志
    end
```



### 完整性保护

```mermaid
graph LR
    A[原始日志] --> B[AES-128-GCM加密]
    B --> C[密文 + 认证标签]
    C --> D[Base64编码]
    D --> E[输出]
    
    F[加密日志] --> G[Base64解码]
    G --> H[AES-128-GCM解密]
    H --> I{认证标签验证}
    I -->|通过| J[原始日志]
    I -->|失败| K[解密失败]
```

## 使用指南

### 基本用法

```bash
# 生成加密日志（必须提供UID）
mlogcat -o -u "my-secret-uid-123" > encrypted.log

# 解密日志（自动从文件读取UID）
mlogdeobf encrypted.log

# 实时加密和解密
mlogcat -o -u "my-secret-uid-123" | mlogdeobf
```

### 高级用法

```bash
# 加密特定模块日志
mlogcat -o -u "module-uid-456" -s MINIEYE:I > module_encrypted.log

# 加密并保存到文件（支持日志轮转，每个新文件都会输出UID头）
mlogcat -o -u "file-uid-789" -f /data/logs/encrypted.log -r 1024 -n 5

# 过滤解密后的日志
mlogcat -o -u "filter-uid-abc" | mlogdeobf | grep "ERROR"

# 解密并保存到文件
mlogdeobf encrypted.log > decrypted.log
```

## 文件格式

### 加密日志格式

```
--------- beginning of main
MLOG_UID:my-secret-uid-123
oYWc4E3IHi9mmMWUvMNDHbAOhBsJ7+rwv2MLES9O8XtFYrYO32TkNqvNBT8WLkoX...
oYWc4E3IHi9mmMWUnEjj+flPjqMyQ71I8drFTTJS8WZZb6kRyHa+af6dRWlKcQ0X...
--------- switch to system
oYWc4E3IHi9mmMWUvMNDHbAOhBsJ7+rwv2MLES9O8XtFYrYO32TkNqvNBT8WLkoX...
```

- **第一行**: `MLOG_UID:` + 用户提供的UID字符串
- **分隔符行**: `--------- beginning of` 或 `--------- switch to` 等系统分隔符（明文输出）
- **日志行**: Base64编码的加密日志内容（IV+Tag+Ciphertext）

### UID头部结构

```
MLOG_UID:[用户提供的UID字符串]
```

