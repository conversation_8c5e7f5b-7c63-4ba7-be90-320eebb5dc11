# mlog日志混淆功能指南

## 概述

mlog日志混淆功能提供了日志加密解决方案，采用UUID派生AES-128-GCM加密算法，确保敏感日志信息的安全性。该功能通过`mlogcat -o -u <uuid>`和`mlogdeobf`工具实现日志的加密和解密。

## 技术架构

### 加密算法

- **UUID派生**: 使用用户提供的UUID通过复杂变换生成AES密钥
- **AES-128-GCM**: 对称加密，用于加密日志内容，提供认证加密
- **确定性密钥生成**: 相同UUID永远生成相同的AES密钥

### 密钥管理

```mermaid
graph TB
    A[用户提供UUID] --> B[SHA256哈希]
    A --> C[反向UUID]
    C --> D[SHA256哈希]
    B --> E[h1: SHA256结果]
    D --> F[h2: SHA256结果]
    E --> G[h1 || h2]
    F --> G
    G --> H[SHA256哈希]
    H --> I[h3: SHA256结果]
    E --> J[位运算混合]
    F --> J
    I --> J
    J --> K[16字节AES-128密钥]
```

### UUID密钥派生算法详解

1. **第一步**: 对原始UUID进行SHA256哈希 → h1
2. **第二步**: 将UUID反向后进行SHA256哈希 → h2
3. **第三步**: 将h1和h2拼接后进行SHA256哈希 → h3
4. **第四步**: 位运算混合生成16字节密钥
   - 从三个摘要按不同偏移取字节：a=h1[i], b=h2[(i+8)%32], c=h3[(i*3)%32]
   - 对b进行循环左移1位：rot = (b<<1)|(b>>7)
   - 异或混合：key[i] = a ^ rot ^ (c + i)

## 工作流程

### 加密流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant M as mlogcat
    participant L as LogObfuscator
    participant A as AES模块

    U->>M: mlogcat -o -u <uuid>
    M->>L: 设置UUID
    M->>L: 初始化混淆器
    L->>L: 从UUID派生AES-128密钥
    L->>M: 输出UUID头部
    M->>U: MLOG_UUID:<uuid>

    loop 每条日志
        M->>L: 原始日志内容
        L->>A: 使用AES-128-GCM加密(随机IV)
        A-->>L: 加密后的日志
        L->>M: base64编码的密文
        M->>U: 输出加密日志
    end
```

### 解密流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant D as mlogdeobf
    participant L as LogObfuscator
    participant A as AES模块

    U->>D: 加密日志输入
    D->>L: 解析UUID头部
    L->>L: 从UUID派生AES-128密钥

    loop 每条加密日志
        D->>L: base64密文
        L->>A: 使用AES-128-GCM解密
        A-->>L: 原始日志内容
        L->>D: 明文日志
        D->>U: 输出原始日志
    end
```

## 安全特性

### UUID密钥派生的安全性

1. **确定性**: 相同的UUID总是生成相同的AES密钥，确保解密的一致性
2. **不可逆性**: 基于SHA256的单向哈希函数，无法从密钥反推出原始UUID
3. **雪崩效应**: UUID的微小变化会导致完全不同的密钥
4. **抗碰撞**: 使用多重SHA256哈希和位运算混合，降低密钥碰撞概率

### 位运算混合算法

密钥生成的最后一步采用复杂的位运算混合：

```cpp
for (size_t i = 0; i < 16; ++i) {
    uint8_t a = h1[i];                    // 从第一个SHA256摘要取字节
    uint8_t b = h2[(i + 8) % 32];         // 从第二个摘要偏移8位置取字节
    uint8_t c = h3[(i * 3) % 32];         // 从第三个摘要按3倍索引取字节
    uint8_t rot = (b << 1) | (b >> 7);    // 循环左移1位
    key[i] = a ^ rot ^ (c + i);           // 异或混合并加入位置索引
}
```

这种设计确保：
- 每个密钥字节都依赖于三个不同的SHA256摘要
- 循环移位增加了非线性变换
- 位置索引确保即使摘要相同，不同位置的密钥字节也不同

### 日志轮转支持

- **标准输出**: UUID头部仅在开始时输出一次
- **文件输出**: 每次日志轮转创建新文件时，都会在文件开头重新输出UUID头部
- **解密兼容**: mlogdeobf自动识别每个文件的UUID头部，支持轮转后的文件解密

## 安全特性

### 密钥保护

1. **编译时混淆**: RSA密钥在编译时经过多层混淆算法处理
2. **静态分析防护**: 混淆算法增加逆向工程难度
3. **运行时解混淆**: 程序运行时动态恢复密钥

### 加密强度

- **RSA3072**: 提供等效于128位对称密钥的安全强度
- **AES-128-GCM**: 提供加密和完整性验证
- **动态密钥**: 每次运行生成新的AES密钥

### 完整性保护

```mermaid
graph LR
    A[原始日志] --> B[AES-128-GCM加密]
    B --> C[密文 + 认证标签]
    C --> D[Base64编码]
    D --> E[输出]
    
    F[加密日志] --> G[Base64解码]
    G --> H[AES-128-GCM解密]
    H --> I{认证标签验证}
    I -->|通过| J[原始日志]
    I -->|失败| K[解密失败]
```

## 使用指南

### 基本用法

```bash
# 生成加密日志（必须提供UUID）
mlogcat -o -u "my-secret-uuid-123" > encrypted.log

# 解密日志（自动从文件读取UUID）
mlogdeobf encrypted.log

# 实时加密和解密
mlogcat -o -u "my-secret-uuid-123" | mlogdeobf
```

### 高级用法

```bash
# 加密特定模块日志
mlogcat -o -u "module-uuid-456" -s MINIEYE:I > module_encrypted.log

# 加密并保存到文件（支持日志轮转，每个新文件都会输出UUID头）
mlogcat -o -u "file-uuid-789" -f /data/logs/encrypted.log -r 1024 -n 5

# 过滤解密后的日志
mlogcat -o -u "filter-uuid-abc" | mlogdeobf | grep "ERROR"

# 解密并保存到文件
mlogdeobf encrypted.log > decrypted.log
```

## 文件格式

### 加密日志格式

```
MLOG_UUID:my-secret-uuid-123
oYWc4E3IHi9mmMWUvMNDHbAOhBsJ7+rwv2MLES9O8XtFYrYO32TkNqvNBT8WLkoX...
oYWc4E3IHi9mmMWUnEjj+flPjqMyQ71I8drFTTJS8WZZb6kRyHa+af6dRWlKcQ0X...
```

- **第一行**: `MLOG_UUID:` + 用户提供的UUID字符串
- **后续行**: Base64编码的加密日志内容（IV+Tag+Ciphertext）

### UUID头部结构

```
MLOG_UUID:[用户提供的UUID字符串]
```

### 加密数据结构

每行加密日志的Base64数据包含：
- **IV (12字节)**: AES-GCM初始化向量，每行随机生成
- **Tag (16字节)**: AES-GCM认证标签
- **Ciphertext (变长)**: AES-GCM加密的日志内容

## 使用注意事项

### 命令行参数

- **必需参数**: 使用`-o`启用加密时，必须同时提供`-u <uuid>`参数
- **UUID格式**: UUID可以是任意长度的字符串，建议使用有意义且安全的标识符
- **参数验证**: 程序启动时会检查参数完整性，缺少UUID时会报错退出

### 兼容性说明

- **格式变更**: 新版本使用`MLOG_UUID:`头部，与旧版本的`MLOG_ENCRYPTED_KEY:`格式不兼容
- **工具版本**: 确保mlogcat和mlogdeobf使用相同版本，避免格式不匹配
- **UUID保密**: UUID相当于密钥，需要妥善保管，避免泄露

### 性能考虑

- **密钥派生**: UUID到AES密钥的派生仅在初始化时执行一次，对性能影响极小
- **加密开销**: 每行日志的AES-GCM加密开销约为明文的1.5-2倍
- **文件大小**: Base64编码会增加约33%的文件大小

### 故障排除

```bash
# 检查是否正确提供UUID
mlogcat -o -u "test-uuid" -d

# 验证加密日志格式
head -1 encrypted.log  # 应显示: MLOG_UUID:test-uuid

# 测试解密功能
echo "MLOG_UUID:test-uuid" > test.log
mlogcat -o -u "test-uuid" -d | tail -n +2 >> test.log
mlogdeobf test.log
```

其中RSA加密的内容包含：
- AES-128密钥 (16字节)
- AES-128 IV (12字节)

## 性能考虑

### 加密性能

- **RSA加密**: 仅在启动时执行一次，性能影响极小
- **AES加密**: 高效的对称加密，适合大量数据处理
- **内存占用**: 增加约50KB静态内存占用

## 故障排除

### 常见问题

1. **解密失败**: 检查密钥头部是否完整
2. **性能问题**: 考虑使用管道而非文件方式
3. **编译错误**: 确保OpenSSL库正确链接
