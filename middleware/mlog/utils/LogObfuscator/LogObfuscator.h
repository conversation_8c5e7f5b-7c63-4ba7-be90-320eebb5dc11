/*
 * Copyright [2025] MINIEYE
 * Descripttion : 日志混淆器，使用UID+AES-128-GCM进行日志加密
 * Author       : xu<PERSON>esen
 * Date         : 2025-08-06
 */

#pragma once

#include <string>
#include <vector>
#include <cstdint>

namespace minieye {
namespace mlog {

class LogObfuscator {
 public:
    // 混淆
    static std::string obfuscate(const std::string& logLine);

    // 反混淆
    static std::string deobfuscate(const std::string& obfuscatedLine);

    // 初始化
    static bool initialize();

    // 获取/设置UID
    static std::string getUID();
    static bool setUID(const std::string& uid);

 private:
    static std::vector<uint8_t> s_aesKey;  // 当前AES密钥
    static std::string s_uid;             // 当前UID
    static bool s_initialized;             // 是否已初始化

    // 从UID生成AES密钥
    static std::vector<uint8_t> generateAESKeyFromUID(const std::string& uid);

    // UID格式验证
    static bool isValidUID(const std::string& uid);

    static std::string base64Encode(const std::vector<uint8_t>& data);
    static std::vector<uint8_t> base64Decode(const std::string& encoded);
};

}  // namespace mlog
}  // namespace minieye
