/*
 * Copyright [2025] MINIEYE
 * Descripttion : 日志混淆器，使用UID+AES-128-GCM进行日志加密
 * Author       : xujiesen
 * Date         : 2025-08-06
 */

#include "LogObfuscator.h"
#include <iostream>
#include <algorithm>

#ifdef USE_OPENSSL_CRYPTO
#include "MiniAESGCM.h"
#include <openssl/sha.h>
#endif

namespace minieye {
namespace mlog {

std::vector<uint8_t> LogObfuscator::s_aesKey;
std::string LogObfuscator::s_uid;
bool LogObfuscator::s_initialized = false;

#ifdef USE_OPENSSL_CRYPTO
// ============================================================================
// OpenSSL实现版本
// ============================================================================

std::string LogObfuscator::obfuscate(const std::string& logLine) {
    if (!s_initialized) {
        if (!initialize()) {
            return logLine; // 如果初始化失败，返回原始日志
        }
    }

    // 为每条日志生成新的IV
    std::vector<uint8_t> iv = MiniAESGCM::generateIV();

    // 使用AES-GCM加密日志
    std::vector<uint8_t> ciphertext;
    std::vector<uint8_t> tag;

    if (!MiniAESGCM::encryptString(logLine, s_aesKey, iv, ciphertext, tag)) {
        std::cerr << "AES加密失败" << std::endl;
        return logLine;
    }

    // 组合数据：IV(12) + Tag(16) + Ciphertext
    std::vector<uint8_t> combined;
    combined.insert(combined.end(), iv.begin(), iv.end());
    combined.insert(combined.end(), tag.begin(), tag.end());
    combined.insert(combined.end(), ciphertext.begin(), ciphertext.end());

    return base64Encode(combined);
}

std::string LogObfuscator::deobfuscate(const std::string& obfuscatedLine) {
    if (obfuscatedLine.empty()) {
        return obfuscatedLine;
    }

    if (!s_initialized) {
        if (!initialize()) {
            return obfuscatedLine;
        }
    }

    // Base64解码
    std::vector<uint8_t> combined = base64Decode(obfuscatedLine);

    if (combined.size() < MiniAESGCM::AES_IV_SIZE + MiniAESGCM::AES_TAG_SIZE) {
        std::cerr << "混淆数据格式错误" << std::endl;
        return obfuscatedLine;
    }

    // 分离IV、Tag和密文
    std::vector<uint8_t> iv(combined.begin(), combined.begin() + MiniAESGCM::AES_IV_SIZE);
    std::vector<uint8_t> tag(combined.begin() + MiniAESGCM::AES_IV_SIZE,
                             combined.begin() + MiniAESGCM::AES_IV_SIZE + MiniAESGCM::AES_TAG_SIZE);
    std::vector<uint8_t> ciphertext(combined.begin() + MiniAESGCM::AES_IV_SIZE + MiniAESGCM::AES_TAG_SIZE,
                                    combined.end());

    // 解密
    std::string plaintext;
    if (!MiniAESGCM::decryptString(ciphertext, s_aesKey, iv, tag, plaintext)) {
        std::cerr << "AES解密失败" << std::endl;
        return obfuscatedLine;
    }

    return plaintext;
}

bool LogObfuscator::initialize() {
    if (!s_initialized) {
        if (!isValidUID(s_uid)) {
            std::cerr << "UID未设置或非法" << std::endl;
            return false;
        }
        s_aesKey = generateAESKeyFromUID(s_uid);
        if (s_aesKey.size() != MiniAESGCM::AES_KEY_SIZE) {
            std::cerr << "从UID生成AES密钥失败" << std::endl;
            return false;
        }
        s_initialized = true;
    }
    return true;
}

std::string LogObfuscator::getUID() {
    return s_uid;
}

bool LogObfuscator::setUID(const std::string& uid) {
    s_uid = uid;
    s_initialized = false;  // 需要重新生成密钥
    return true;
}

std::vector<uint8_t> LogObfuscator::generateAESKeyFromUID(const std::string& uid) {
    std::vector<uint8_t> key(MiniAESGCM::AES_KEY_SIZE, 0);
    if (uid.empty()) return key;

    unsigned char h1[32];
    unsigned char h2[32];
    unsigned char h3[32];

    // 步骤1：sha256哈希
    SHA256(reinterpret_cast<const unsigned char*>(uid.data()), uid.size(), h1);

    // 步骤2：sha256哈希反向的uid
    std::string rev = uid;
    std::reverse(rev.begin(), rev.end());
    SHA256(reinterpret_cast<const unsigned char*>(rev.data()), rev.size(), h2);

    // 步骤3：sha256哈希h1+h2
    unsigned char h12[64];
    for (int i = 0; i < 32; ++i) {
        h12[i] = h1[i];
        h12[32 + i] = h2[i];
    }
    SHA256(h12, sizeof(h12), h3);

    // 位运算混合生成16字节密钥
    for (size_t i = 0; i < MiniAESGCM::AES_KEY_SIZE; ++i) {
        uint8_t a = h1[i];
        uint8_t b = h2[(i + 8) % 32];
        uint8_t c = h3[(i * 3) % 32];
        uint8_t rot = static_cast<uint8_t>((b << 1) | (b >> 7));
        key[i] = static_cast<uint8_t>(a ^ rot ^ (c + static_cast<uint8_t>(i)));
    }
    return key;
}

bool LogObfuscator::isValidUID(const std::string& uid) {
    return !uid.empty();
}

std::string LogObfuscator::base64Encode(const std::vector<uint8_t>& data) {
    static const char base64_chars[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";

    std::string encoded;
    int val = 0, valb = -6;
    for (uint8_t c : data) {
        val = (val << 8) + c;
        valb += 8;
        while (valb >= 0) {
            encoded.push_back(base64_chars[(val >> valb) & 0x3F]);
            valb -= 6;
        }
    }
    if (valb > -6) {
        encoded.push_back(base64_chars[((val << 8) >> (valb + 8)) & 0x3F]);
    }
    while (encoded.size() % 4) {
        encoded.push_back('=');
    }
    return encoded;
}

std::vector<uint8_t> LogObfuscator::base64Decode(const std::string& encoded) {
    static const int T[128] = {
        -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1,
        -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1,
        -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,62, -1,-1,-1,63,
        52,53,54,55, 56,57,58,59, 60,61,-1,-1, -1,-2,-1,-1,
        -1, 0, 1, 2,  3, 4, 5, 6,  7, 8, 9,10, 11,12,13,14,
        15,16,17,18, 19,20,21,22, 23,24,25,-1, -1,-1,-1,-1,
        -1,26,27,28, 29,30,31,32, 33,34,35,36, 37,38,39,40,
        41,42,43,44, 45,46,47,48, 49,50,51,-1, -1,-1,-1,-1
    };

    std::vector<uint8_t> decoded;
    int val = 0, valb = -8;
    for (char c : encoded) {
        if ((c & 0x7F) >= 128 || T[c & 0x7F] < 0) break;
        val = (val << 6) + T[c & 0x7F];
        valb += 6;
        if (valb >= 0) {
            decoded.push_back((val >> valb) & 0xFF);
            valb -= 8;
        }
    }
    return decoded;
}

#endif // USE_OPENSSL_CRYPTO

} // namespace mlog
} // namespace minieye
